const esbuild = require("esbuild");

const config = {
  entryPoints: ["src/index.ts"],
  bundle: true,
  outdir: "dist",
  platform: "node",
  target: "node16",
  format: "cjs",
  sourcemap: true,
  resolveExtensions: [".ts", ".js", ".json"],
  loader: {
    ".ts": "ts",
    ".js": "js",
    ".json": "json",
  },
  external: [],
  minify: false,
  splitting: false,
  metafile: true,
  logLevel: "info",
};

async function build() {
  try {
    const result = await esbuild.build(config);
    console.log("Build completed successfully!");
    if (result.metafile) {
      console.log("Metafile generated");
    }
  } catch (error) {
    console.error("Build failed:", error);
    process.exit(1);
  }
}

async function watch() {
  const ctx = await esbuild.context({
    ...config,
    minify: false,
  });

  await ctx.watch();
  console.log("Watching for changes...");
}

module.exports = {
  config,
  build,
  watch,
};

if (require.main === module) {
  const command = process.argv[2];

  if (command === "watch") {
    watch();
  } else {
    build();
  }
}
